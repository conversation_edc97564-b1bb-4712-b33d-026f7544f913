import { getCachedGlobal } from '@/utilities/getGlobals'
import Link from 'next/link'
import React from 'react'

import type { Footer } from '@/payload-types'

import { ThemeSelector } from '@/providers/Theme/ThemeSelector'
import { CMSLink } from '@/components/Link'
import { Logo } from '@/components/Logo/Logo'

export async function Footer() {
  const footerData: Footer = await getCachedGlobal('footer', 1)()

  const navItems = footerData?.navItems || []
  const { disclaimer, copyright } = footerData || {}

  return (
    <footer className="mt-auto border-t border-border bg-black dark:bg-card text-white">
      <div className="container py-8">
        {/* Main footer content */}
        <div className="flex flex-col md:flex-row md:justify-between gap-8 mb-6">
          <Link className="flex items-center" href="/">
            <Logo />
          </Link>

          <div className="flex flex-col-reverse items-start md:flex-row gap-4 md:items-center">
            <ThemeSelector />
            <nav className="flex flex-col md:flex-row gap-4">
              {navItems.map(({ link }, i) => {
                return <CMSLink className="text-white" key={i} {...link} />
              })}
            </nav>
          </div>
        </div>

        {/* Disclaimer and Copyright section */}
        <div className="border-t border-gray-700 pt-6 flex flex-col md:flex-row md:justify-between gap-4 text-sm text-gray-300">
          {disclaimer && (
            <div className="disclaimer">
              <p>{disclaimer}</p>
            </div>
          )}

          {copyright && (
            <div className="copyright">
              <p>{copyright}</p>
            </div>
          )}
        </div>
      </div>
    </footer>
  )
}
