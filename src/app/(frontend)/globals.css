@tailwind base;
@tailwind components;
@tailwind utilities;

/*
  Copyright (C) Hoefler & Co.
  This software is the property of Hoefler & Co. (H&Co).
  Your right to access and use this software is subject to the
  applicable License Agreement, or Terms of Service, that exists
  between you and H&Co. If no such agreement exists, you may not
  access or use this software for any purpose.
  This software may only be hosted at the locations specified in
  the applicable License Agreement or Terms of Service, and only
  for the purposes expressly set forth therein. You may not copy,
  modify, convert, create derivative works from or distribute this
  software in any way, or make it accessible to any third party,
  without first obtaining the written permission of H&Co.
  For more information, please visit us at http://typography.com.
*/

@font-face {
  font-family: 'Ringside Regular A';
  src: url('https://cdn.elizabethwarren.com/_public/fonts/RingsideRegular-Book_Web.woff2') format('woff2'), url('https://cdn.elizabethwarren.com/_public/fonts/RingsideRegular-Book_Web.woff') format('woff');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'Ringside Regular A';
  src: url('https://cdn.elizabethwarren.com/_public/fonts/RingsideRegular-BookItalic_Web.woff2') format('woff2'), url('https://cdn.elizabethwarren.com/_public/fonts/RingsideRegular-BookItalic_Web.woff') format('woff');
  font-weight: 400;
  font-style: italic;
}

@font-face {
  font-family: 'Ringside Regular A';
  src: url('https://cdn.elizabethwarren.com/_public/fonts/RingsideRegular-Bold_Web.woff2') format('woff2'), url('https://cdn.elizabethwarren.com/_public/fonts/RingsideRegular-Bold_Web.woff') format('woff');
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: 'Ringside Regular A';
  src: url('https://cdn.elizabethwarren.com/_public/fonts/RingsideRegular-BoldItalic_Web.woff2') format('woff2'), url('https://cdn.elizabethwarren.com/_public/fonts/RingsideRegular-BoldItalic_Web.woff') format('woff');
  font-weight: 700;
  font-style: italic;
}

@font-face {
  font-family: 'Ringside Extra Wide SSm A';
  src: url('https://cdn.elizabethwarren.com/_public/fonts/RingsideExtraWideSSm-Black_Web.woff2') format('woff2'), url('https://cdn.elizabethwarren.com/_public/fonts/RingsideExtraWideSSm-Black_Web.woff') format('woff');
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'Ringside Compressed A';
  src: url('https://cdn.elizabethwarren.com/_public/fonts/RingsideCompressed-Bold_Web.woff2') format('woff2'), url('https://cdn.elizabethwarren.com/_public/fonts/RingsideCompressed-Bold_Web.woff') format('woff');
  font-weight: 700;
  font-style: normal;
}

@import url("https://use.typekit.net/yli8oct.css");

@layer utilities {
  .font-regular-book {
    font-family: "Ringside Regular A", "Ringside Regular B";
    font-style: normal;
    font-weight: 400;
  }

  .font-regular-book-italic {
    font-family: "Ringside Regular A", "Ringside Regular B";
    font-style: italic;
    font-weight: 400;
  }

  .font-regular-book-bold {
    font-family: "Ringside Regular A", "Ringside Regular B";
    font-style: normal;
    font-weight: 700;
  }

  .font-regular-book-bold-italic {
    font-family: "Ringside Regular A", "Ringside Regular B";
    font-style: italic;
    font-weight: 700;
  }

  .font-compressed-bold {
    font-family: "Ringside Compressed A", "Ringside Compressed B";
    font-style: normal;
    font-weight: 700;
  }

  .font-extra-wide {
    font-family: "Ringside Extra Wide SSm A", "Ringside Extra Wide SSm B";
    font-style: normal;
    font-weight: 800;
  }

  .font-serif-book {
    font-family: freight-text-pro, Georgia, serif;
    font-weight: 400;
    font-style: normal;
  }

  .font-serif-book-italic {
    font-family: freight-text-pro, Georgia, serif;
    font-weight: 400;
    font-style: italic;
  }

  .font-serif-bold {
    font-family: freight-text-pro, Georgia, serif;
    font-weight: 700;
    font-style: normal;
  }

  .font-serif-bold-italic {
    font-family: freight-text-pro, Georgia, serif;
    font-weight: 700;
    font-style: italic;
  }
}

@layer base {
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-size: unset;
    font-weight: unset;
  }

  :root {
    --Primary-Navy: #182658;
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 240 5% 96%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 240 6% 80%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.2rem;

    --success: 196 52% 74%;
    --warning: 34 89% 85%;
    --error: 10 100% 86%;
  }

  [data-theme='dark'] {
    --background: 0 0% 0%;
    --foreground: 210 40% 98%;

    --card: 0 0% 4%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 0, 0%, 15%, 0.8;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;

    --success: 196 100% 14%;
    --warning: 34 51% 25%;
    --error: 10 39% 43%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground min-h-[100vh] flex flex-col;
  }
}

html {
  opacity: 0;
}

html[data-theme='dark'],
html[data-theme='light'] {
  opacity: initial;
}
