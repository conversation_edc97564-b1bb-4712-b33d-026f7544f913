import clsx from 'clsx'
import React from 'react'

interface Props {
  className?: string
  loading?: 'lazy' | 'eager'
  priority?: 'auto' | 'high' | 'low'
}

export const Logo = (props: Props) => {
  const { loading: loadingFromProps, priority: priorityFromProps, className } = props

  const loading = loadingFromProps || 'lazy'
  const priority = priorityFromProps || 'low'

  return (
    /* eslint-disable @next/next/no-img-element */
    <svg 
      width={101}
      height={58}
      viewBox="0 0 101 58" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#a)">
        <path fill="#fff" d="M17.601 2.404v5.01a.31.31 0 0 1-.344.309l-10.28-1.11V15.4l7.811.523a.31.31 0 0 1 .287.307v4.922a.314.314 0 0 1-.32.312l-7.783-.21v18.172a.31.31 0 0 1-.263.308l-5.443.866a.314.314 0 0 1-.36-.308V.312A.326.326 0 0 1 1.265 0l16.068 2.097a.31.31 0 0 1 .271.307h-.004ZM27.642 3.254v34.193a.31.31 0 0 1-.287.308l-5.59.416a.31.31 0 0 1-.331-.307V2.834c0-.183.153-.325.331-.308l5.59.42a.31.31 0 0 1 .287.308ZM52.377 13.365v1.777c0 .17-.138.311-.312.311h-5.298a.312.312 0 0 1-.312-.311v-.951c0-3.396-1.263-5.145-3.93-5.145-2.667 0-4.076 1.89-4.076 4.902v13.097c0 3.056 1.603 4.898 3.979 4.898 2.376 0 3.979-1.793 3.979-5.044v-2.618H43.03a.312.312 0 0 1-.312-.312v-3.893c0-.17.138-.312.312-.312h9.228c.17 0 .312.137.312.312V36.29c0 .17-.138.311-.312.311h-2.444a.312.312 0 0 1-.304-.247l-.87-4.262c-1.214 2.62-3.396 5.14-7.617 5.14-5.286 0-8.877-3.88-8.877-10.381V14.14c0-6.548 4.27-10.527 10.285-10.527 6.015 0 9.945 3.3 9.945 9.75ZM63.43 3.675v12.968h7.905l.114-13.466c0-.158.12-.291.279-.307l5.59-.518a.31.31 0 0 1 .34.307l-.114 35.29a.31.31 0 0 1-.336.308l-5.59-.498a.315.315 0 0 1-.283-.308V22.366h-7.904v14.7c0 .179-.15.32-.324.308l-5.59-.283a.31.31 0 0 1-.295-.308V3.954c0-.166.13-.3.295-.307l5.59-.284a.309.309 0 0 1 .324.308v.004ZM100.825.364l.008 5.153a.31.31 0 0 1-.279.307l-6.383.632-.044 34.035c0 .19-.17.336-.36.307l-5.59-.927a.31.31 0 0 1-.26-.307L87.95 7.09l-6.435.632a.308.308 0 0 1-.34-.308V2.36c0-.158.121-.292.28-.308L100.48.057a.312.312 0 0 1 .344.307Z"/>
        <path fill="#B7E4CF" d="M35.422 44.453c3.716 0 5.918 1.769 5.918 4.586s-2.323 4.667-5.998 4.667H31.59v3.731c0 .134-.11.243-.243.243h-3.51a.244.244 0 0 1-.242-.243V44.692c0-.134.109-.243.243-.243h7.584v.004Zm-3.853 6.334h3.157c1.947 0 2.643-.655 2.643-1.687s-.736-1.688-2.562-1.688H31.57v3.375ZM53.207 55.515h-7.152l-.968 2.028a.241.241 0 0 1-.218.137h-3.51a.24.24 0 0 1-.214-.348L47.52 44.53a.243.243 0 0 1 .214-.133h4.072c.093 0 .174.052.215.133l6.374 12.802a.24.24 0 0 1-.214.348h-3.768a.241.241 0 0 1-.219-.137l-.987-2.028Zm-5.877-2.623h4.626l-2.283-4.768h-.04l-2.303 4.768ZM74.229 46.1l-1.336 2.437a.24.24 0 0 1-.32.097c-1.335-.688-2.825-1.263-4.598-1.263-2.918 0-4.646 1.51-4.646 3.675 0 2.166 1.866 3.716 4.788 3.716 1.728 0 3.173-.57 4.452-1.27a.238.238 0 0 1 .328.1l1.267 2.429a.248.248 0 0 1-.097.328C72.31 57.304 70.319 58 67.78 58c-5.282 0-8.423-2.898-8.423-6.933 0-4.036 3.218-6.934 8.443-6.934 2.542 0 4.46.644 6.335 1.631a.244.244 0 0 1 .1.332l-.007.005ZM20.88 49.687H.345a.215.215 0 0 0-.214.214v2.841c0 .***************.215H20.88a.215.215 0 0 0 .215-.215v-2.841a.215.215 0 0 0-.215-.215ZM99.886 49.687H79.353a.215.215 0 0 0-.214.214v2.841c0 .***************.215h20.533a.214.214 0 0 0 .214-.215v-2.841a.214.214 0 0 0-.214-.215Z"/>
      </g>
      <defs>
        <clipPath id="a"><path fill="#fff" d="M.132 0h100.7v58H.133z"/></clipPath>
      </defs>
    </svg>
  )
}