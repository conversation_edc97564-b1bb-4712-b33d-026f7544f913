import React from 'react'

export const FontTest: React.FC = () => {
  return (
    <div className="p-8 space-y-4">
      <h2 className="text-2xl font-bold mb-4">Font Loading Test</h2>
      
      {/* Test Ringside fonts */}
      <div className="space-y-2">
        <h3 className="font-bold">Ringside Fonts:</h3>
        <p className="font-regular-book">Ringside Regular Book - The quick brown fox</p>
        <p className="font-regular-book-bold">Ringside Regular Bold - The quick brown fox</p>
        <p className="font-compressed-bold">Ringside Compressed Bold - THE QUICK BROWN FOX</p>
        <p className="font-extra-wide">Ringside Extra Wide - THE QUICK BROWN FOX</p>
      </div>

      {/* Test TypeKit fonts */}
      <div className="space-y-2">
        <h3 className="font-bold">TypeKit Fonts:</h3>
        <p className="font-serif-book">FreightText Pro Book - The quick brown fox jumps over the lazy dog</p>
        <p className="font-serif-bold">FreightText Pro Bold - The quick brown fox jumps over the lazy dog</p>
        <p className="debug-typekit">Debug TypeKit - The quick brown fox jumps over the lazy dog</p>
      </div>

      {/* Test with direct font-family */}
      <div className="space-y-2">
        <h3 className="font-bold">Direct Font Family:</h3>
        <p style={{ fontFamily: 'freight-text-pro, Georgia, serif' }}>
          Direct freight-text-pro - The quick brown fox jumps over the lazy dog
        </p>
        <p style={{ fontFamily: '"freight-text-pro", Georgia, serif' }}>
          Direct "freight-text-pro" - The quick brown fox jumps over the lazy dog
        </p>
      </div>

      {/* Prose test */}
      <div className="prose">
        <h3>Prose Test:</h3>
        <p>This paragraph should use FreightText Pro from the prose configuration.</p>
      </div>
    </div>
  )
}
