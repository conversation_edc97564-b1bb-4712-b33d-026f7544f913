import React from 'react'
import type { Page } from '@/payload-types'
import { RenderBlocks } from '@/blocks/RenderBlocks'
import { RenderHero } from '@/heros/RenderHero'
import { getCachedHomePage } from '@/utilities/getHomePage'

/**
 * Server Component that renders the home page content from CMS
 * This replaces hard-coded content with dynamic CMS data
 */
export const HomePageContent: React.FC = async () => {
  // Fetch the home page data from CMS
  const getHomePageData = getCachedHomePage(2) // depth 2 to include media relations
  const homePage = await getHomePageData()

  if (!homePage) {
    return (
      <div className="container mx-auto px-4 py-16">
        <h1 className="text-2xl font-bold text-center">
          Home page not found. Please create a page with slug "home" in the CMS.
        </h1>
      </div>
    )
  }

  const { hero, layout } = homePage

  return (
    <article className="pt-16 pb-24">
      {/* Render the hero section from CMS */}
      <RenderHero {...hero} />
      
      {/* Render the layout blocks from CMS */}
      <RenderBlocks blocks={layout} />
    </article>
  )
}

/**
 * Client Component version for when you need to fetch data on the client side
 * Note: This is less performant than the server component above
 */
'use client'
import { useState, useEffect } from 'react'

export const HomePageContentClient: React.FC = () => {
  const [homePage, setHomePage] = useState<Page | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchHomePage = async () => {
      try {
        // You would need to create an API route for this
        const response = await fetch('/api/pages/home')
        if (!response.ok) {
          throw new Error('Failed to fetch home page')
        }
        const data = await response.json()
        setHomePage(data)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
      } finally {
        setLoading(false)
      }
    }

    fetchHomePage()
  }, [])

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="text-center">Loading...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="text-center text-red-600">Error: {error}</div>
      </div>
    )
  }

  if (!homePage) {
    return (
      <div className="container mx-auto px-4 py-16">
        <h1 className="text-2xl font-bold text-center">
          Home page not found. Please create a page with slug "home" in the CMS.
        </h1>
      </div>
    )
  }

  const { hero, layout } = homePage

  return (
    <article className="pt-16 pb-24">
      <RenderHero {...hero} />
      <RenderBlocks blocks={layout} />
    </article>
  )
}
